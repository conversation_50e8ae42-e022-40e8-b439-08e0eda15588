# analysis/response_strength_analysis.R
# This script is the main entry point called by Python.
# It sources the Killeen framework functions and then calls a main execution block.

# Set working directory to the script's location for consistent relative paths
# This is important if Rscript is called from a different directory
# tryCatch({
#   # script_path <- normalizePath(sys.frames()[[1]]$ofile)
#   # if running via Rscript, this is better:
#   args_tmp <- commandArgs(trailingOnly = FALSE)
#   script_path_arg <- "--file="
#   script_path <- sub(script_path_arg, "", args_tmp[grep(script_path_arg, args_tmp)])
#   if (length(script_path) == 1 && dir.exists(dirname(script_path))) {
#     setwd(dirname(script_path))
#     message(paste("R working directory set to:", getwd()))
#   } else {
#     message("Could not reliably set R working directory. Assuming relative paths are correct from execution context.")
#   }
# }, error = function(e) {
#   message(paste("Warning: Could not set working directory automatically - ", e$message))
# })


# Source the framework functions
# Assuming killeen_framework.R is in the same directory or path is adjusted
source_file_path <- "killeen_framework.R" # If in the same 'analysis' folder
if (file.exists(source_file_path)) {
    source(source_file_path)
    message(paste0("Successfully sourced '", source_file_path, "'"))
} else {
    # Try one level up (if response_strength_analysis.R is in a subfolder of where killeen_framework.R is)
    source_file_path_alt <- file.path("..", source_file_path)
    if(file.exists(source_file_path_alt)) {
        source(source_file_path_alt)
        message(paste0("Successfully sourced '", source_file_path_alt, "'"))
    } else {
        stop(paste0("Cannot find killeen_framework.R. Searched: '", source_file_path, "' and '", source_file_path_alt, "'. Current R wd: ", getwd()))
    }
}

# The main() function and argument parsing are now INSIDE killeen_framework.R
# This script (response_strength_analysis.R) just ensures killeen_framework.R is loaded and its main() is run.
# If killeen_framework.R is designed to run directly, then this wrapper might not be strictly necessary,
# but it can be good for organization if killeen_framework.R becomes a true library file.

# If killeen_framework.R's main() is not automatically called upon sourcing (e.g. if it's not in an if(!interactive()) block there),
# you would call it here:
# if (!interactive()){
#     main_from_killeen_framework() # Assuming you rename main() in killeen_framework.R
# }