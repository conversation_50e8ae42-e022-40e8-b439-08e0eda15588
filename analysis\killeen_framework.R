# analysis/killeen_framework.R

# --- Load necessary packages ---
suppressPackageStartupMessages({
    library(tidyverse)
    library(markovchain)
    library(fitdistrplus)
    library(RSQLite)
    library(DBI)
    library(jsonlite) # For parsing all_response_times from DB
    library(optparse) # For command line arguments
})

# --- Logging setup (simple print statements for Rscript output) ---
log_info <- function(...) { message(paste0("[INFO] ", Sys.time(), " | ", ...)) }
log_warn <- function(...) { message(paste0("[WARN] ", Sys.time(), " | ", ...)) }
log_error <- function(...) { message(paste0("[ERROR] ", Sys.time(), " | ", ...)) }


# --- Argument Parsing ---
option_list <- list(
    make_option(c("-s", "--subject"), type="character", default=NULL, help="Subject ID to analyze", metavar="character"),
    make_option(c("-e", "--session"), type="character", default=NULL, help="Session ID to analyze", metavar="character"),
    make_option(c("-d", "--db_path"), type="character", default=NULL, help="Path to SQLite database file", metavar="character"),
    make_option(c("-o", "--output_json"), type="character", default=NULL, help="Path to save JSON output", metavar="character")
)
opt_parser <- OptionParser(option_list=option_list)
opt <- parse_args(opt_parser)

if (is.null(opt$db_path)) {
    log_error("Database path (--db_path) must be supplied.")
    stop("Database path not specified.", call.=FALSE)
}

# --- Database Connection ---
connect_db <- function(db_path) {
    tryCatch({
        con <- dbConnect(RSQLite::SQLite(), db_path)
        log_info("Successfully connected to database: ", db_path)
        return(con)
    }, error = function(e) {
        log_error("Failed to connect to database: ", db_path, " - Error: ", e$message)
        stop(e)
    })
}

disconnect_db <- function(con) {
    if (dbIsValid(con)) {
        dbDisconnect(con)
        log_info("Database connection closed.")
    }
}

# --- Data Loading from DB ---
load_data_from_db <- function(con, subject_id = NULL, session_id = NULL) {
    query <- "SELECT * FROM trials"
    conditions <- c()
    params <- list()

    if (!is.null(subject_id)) {
        conditions <- c(conditions, "subject_id = :subject_id")
        params$subject_id <- subject_id
    }
    if (!is.null(session_id)) {
        conditions <- c(conditions, "session_id = :session_id")
        params$session_id <- session_id
    }

    if (length(conditions) > 0) {
        query <- paste(query, "WHERE", paste(conditions, collapse = " AND "))
    }
    query <- paste(query, "ORDER BY subject_id, session_id, trial_number;")
    
    log_info("Executing query: ", query)
    log_info("With params: ", paste(names(params), params, sep="=", collapse=", "))

    tryCatch({
        # trials_df <- dbGetQuery(con, query, params = params) # params argument might not work well with all drivers/versions
        # Using dbBind and then dbFetch for more robust parameter binding
        res <- dbSendQuery(con, query)
        if(length(params) > 0) dbBind(res, params)
        trials_df <- dbFetch(res)
        dbClearResult(res)
        
        log_info("Successfully loaded ", nrow(trials_df), " trials from the database.")
        if (nrow(trials_df) == 0) {
            log_warn("No trials found for the given criteria.")
            return(data.frame()) # Return empty dataframe
        }

        # Convert all_response_times from JSON string to numeric vector (list column)
        # And other necessary type conversions
        trials_df <- trials_df %>%
            mutate(
                # Killeen framework expects 'responses' as count of responses in trial
                # The DB has 'multiple_responses' as count. Assuming this is the right one.
                # If 'first_response_time' is non-NA, it implies at least 1 response.
                # 'multiple_responses' from schema has DEFAULT 0, should be total actual responses.
                # Let's assume it's correctly populated by Python side.
                responses = as.integer(multiple_responses), 

                # Latency and trial duration are already in ms in DB, convert to seconds for Killeen script
                time_to_first_response = latency_ms / 1000.0, # This is what prepare_response_data expects
                trial_duration = trial_duration_ms / 1000.0, # This is what prepare_response_data expects
                
                # These are for prepare_response_data if it needs to recalc duration
                trial_start = trial_start_time / 1000.0,
                trial_end = trial_end_time / 1000.0,

                # For 'calculate_rate_probability_mapping's response_times_in_trial_col
                # all_response_times are absolute. Need to make them relative to trial_start for that function.
                # And they are stored as JSON string of timestamps (ms)
                response_times_in_trial = map(all_response_times, ~ {
                    if (is.na(.x) || .x == "") return(numeric(0))
                    parsed_times_ms <- tryCatch(jsonlite::fromJSON(.x), error = function(e) numeric(0))
                    # Convert to seconds and make relative to this trial's start_time
                    # Need to join trial_start_time for this operation
                    return(parsed_times_ms) # Keep as ms for now, will be converted later
                }),
                sd_presentation_time_relative = (sd_presentation_time - trial_start_time) / 1000.0, # in seconds
                response_occurred_binary = as.integer(response_occurred) # Killeen script needs 'response'
            ) %>%
            rename(
                subject = subject_id, # Match Killeen script
                session = session_id,
                trial = trial_number
                # 'response' column will be created by prepare_response_data from 'responses' (count)
            )

        # Make response_times_in_trial relative and in seconds
        trials_df <- trials_df %>%
            mutate(
                response_times_in_trial = map2(response_times_in_trial, trial_start_time, ~ {
                    if (length(.x) > 0) {
                        (.x - .y) / 1000.0 # Convert to seconds and make relative
                    } else {
                        numeric(0)
                    }
                })
            )

        return(trials_df)

    }, error = function(e) {
        log_error("Error loading data from database: ", e$message)
        return(data.frame()) # Return empty dataframe on error
    })
}


#############################################################
# KILLEEN ET AL. FRAMEWORK FUNCTIONS (FROM YOUR REVISED SCRIPT)
# PASTE THE REVISED R FUNCTIONS (prepare_response_data, analyze_markov, etc.) HERE
# Make sure to adjust column names if they differ from what these functions expect
# e.g. the Killeen script expects 'response' to be the binary 0/1,
# 'responses' to be the count, 'time_to_first_response', 'trial_duration'
#############################################################

# --- (Assuming your revised R functions from previous interactions are pasted here) ---
# --- For brevity, I'm including stubs, replace with your full corrected functions ---

prepare_response_data <- function(data, 
                                  responses_count_col = "responses", # This is the count of responses
                                  time_to_first_response_col = "time_to_first_response", # in seconds
                                  trial_duration_col = "trial_duration", # in seconds
                                  trial_start_col = "trial_start", # in seconds
                                  trial_end_col = "trial_end", # in seconds
                                  all_response_times_col = NULL # Not directly used for IRT here
                                  ) {
    # Ensure required columns exist
    required_input_cols <- c("subject", "session", "trial", responses_count_col)
    if(!all(required_input_cols %in% colnames(data))) {
        missing_cols <- required_input_cols[!required_input_cols %in% colnames(data)]
        stop(paste("Input data for prepare_response_data must contain:", paste(missing_cols, collapse=", ")))
    }
    
    processed_data <- data %>%
        group_by(subject, session) %>%
        arrange(subject, session, trial) %>%
        mutate(
            response = ifelse(is.na(.data[[responses_count_col]]) | .data[[responses_count_col]] == 0, 0, 1),
            latency = if(time_to_first_response_col %in% colnames(.)) .data[[time_to_first_response_col]] else NA,
            latency = ifelse(response == 1 & !is.na(latency), latency, NA),
            trial_duration_val = if(trial_duration_col %in% colnames(.)) .data[[trial_duration_col]] else 
                               if(trial_start_col %in% colnames(.) && trial_end_col %in% colnames(.)) .data[[trial_end_col]] - .data[[trial_start_col]] else NA,
            running_rate = ifelse(response == 1 & !is.na(latency) & .data[[responses_count_col]] > 1 & !is.na(trial_duration_val) & trial_duration_val > latency,
                                 (.data[[responses_count_col]] - 1) / (trial_duration_val - latency), 0),
            overall_rate = ifelse(!is.na(trial_duration_val) & trial_duration_val > 0, .data[[responses_count_col]] / trial_duration_val, 0)
        ) %>%
        ungroup()

    # IRT calculation needs all response times FOR THE SESSION for a subject.
    # The DB query loads 'all_response_times' (JSON of timestamps in ms) per trial.
    # These need to be unnested, converted to seconds, made relative to session start (or just diff'd globally),
    # and then IRTs calculated. This is a complex step usually done BEFORE trial aggregation.
    # For this script, we'll assume IRTs are passed as a separate vector to fit_irt_models.
    # The main 'analyze_response_strength' will extract all IRTs for a subject.
    log_info("Data preparation complete. Output columns: ", paste(colnames(processed_data), collapse=", "))
    return(processed_data)
}


# --- (PASTE YOUR OTHER CORRECTED R FUNCTIONS HERE) ---
# analyze_markov, analyze_response_sequences, fit_latency_models, 
# fit_irt_models, calculate_rate_probability_mapping, test_rate_probability_relation
# ... and the main analyze_response_strength_r (renamed from your Python version)

# Example stub for analyze_response_strength_r
analyze_response_strength_r <- function(data_df, 
                                     epoch_length_for_rate_prob = 1,
                                     # Column names expected by prepare_response_data
                                     responses_count_col_r = "responses", # from DB: multiple_responses
                                     time_to_first_response_col_r = "time_to_first_response", # from DB: latency_ms / 1000
                                     trial_duration_col_r = "trial_duration", # from DB: trial_duration_ms / 1000
                                     trial_start_col_r = "trial_start", # from DB: trial_start_time / 1000
                                     trial_end_col_r = "trial_end",   # from DB: trial_end_time / 1000
                                     # For calculate_rate_probability_mapping
                                     response_times_in_trial_col_r = "response_times_in_trial" # from DB: all_response_times (parsed & made relative)
                                     ) {
    log_info("Starting R analysis with Killeen framework...")
    
    # 1. Prepare data (maps DB columns to what Killeen functions expect)
    # The `load_data_from_db` function already renames and converts some columns.
    # `prepare_response_data` will then use these.
    
    # The column `data_df$responses` (count) is used by `prepare_response_data` to create `response` (binary)
    # `data_df$time_to_first_response` (latency in s) is used.
    # `data_df$trial_duration` (in s) is used.
    # These should be correctly named by `load_data_from_db`.
    
    # The Killeen `prepare_response_data` expects column "responses" to be the count.
    # And it calculates "response" (binary), "latency", "running_rate", "overall_rate".
    # Let's ensure the input `data_df` has these names if they are directly from DB.
    # `load_data_from_db` already did this renaming.

    prepared_data <- prepare_response_data(data_df,
                                          responses_count_col = responses_count_col_r,
                                          time_to_first_response_col = time_to_first_response_col_r,
                                          trial_duration_col = trial_duration_col_r,
                                          trial_start_col = trial_start_col_r,
                                          trial_end_col = trial_end_col_r
                                          )
    
    # Setup results structure
    results <- list()
    
    # Perform analyses for each subject (if multiple subjects in data_df)
    subjects_in_data <- unique(prepared_data$subject)
    
    for(subj_id in subjects_in_data) {
        log_info("Analyzing R data for subject: ", subj_id)
        subj_trial_data <- prepared_data[prepared_data$subject == subj_id, ]
        subj_results_r <- list()

        # A. Markov model
        # `analyze_markov` expects a vector of binary responses
        subj_results_r$markov <- analyze_markov(subj_trial_data$response) # 'response' is binary 0/1

        # B. Latency analysis
        subj_results_r$latency <- fit_latency_models(subj_trial_data$latency) # 'latency' in seconds

        # C. IRT analysis
        # This needs a vector of ALL IRTs for the subject.
        # `data_df` (original from DB) has `all_response_times` (JSON of ms timestamps per trial)
        # We need to unnest these, convert to seconds, diff to get IRTs for the whole subject.
        
        # Extracting all response times for the current subject from original `data_df`
        # `data_df` is the full loaded data before filtering by subject for this loop
        all_subj_response_times_ms_lists <- data_df %>%
            filter(subject == subj_id) %>%
            pull(response_times_in_trial) # This is already list of NUMERIC VECTORS (ms, relative) from load_data_from_db

        all_subj_response_times_s_relative_flat <- unlist(all_subj_response_times_ms_lists) / 1000.0
        
        # If response times are relative to trial start, we need to make them continuous for session IRTs
        # This requires knowing trial starts and durations to chain them.
        # For simplicity, IF response_times_in_trial are already ABSOLUTE session times (in seconds) then:
        # irts_for_subject <- data_df %>% 
        #    filter(subject == subj_id) %>%
        #    select(session, response_times_in_trial) %>% # assuming response_times_in_trial is list of ABSOLUTE session times
        #    unnest(response_times_in_trial) %>%
        #    arrange(session, response_times_in_trial) %>%
        #    group_by(session) %>%
        #    mutate(irt = c(NA, diff(response_times_in_trial))) %>%
        #    ungroup() %>%
        #    pull(irt) %>% na.omit()
        #
        # Given the current `load_data_from_db` and `prepare_response_data`,
        # `response_times_in_trial` column in `subj_trial_data` (which is `prepared_data` filtered)
        # contains lists of response times *relative to their trial start*, in seconds.
        # To get session-wide IRTs, we need to make these continuous.
        # This is complex. A simpler approach for the Killeen framework if it pools all IRTs:
        # Just unlist all within-trial IRTs. This isn't perfect but might be what was intended by the original script.
        
        # A more direct way: Calculate IRTs from all_response_times from the database
        # which are absolute timestamps (ms).
        db_irts_for_subject <- data_df %>%
            filter(subject == subj_id) %>%
            select(all_response_times) %>% # This is the JSON string column from DB
            mutate(parsed_rts = map(all_response_times, ~ {
                if(is.na(.x) || .x == "") return(numeric(0))
                tryCatch(jsonlite::fromJSON(.x), error = function(e) numeric(0))
            })) %>%
            unnest(parsed_rts) %>%
            arrange(parsed_rts) %>%
            mutate(irt_ms = c(NA, diff(parsed_rts))) %>%
            pull(irt_ms) %>%
            na.omit() / 1000.0 # Convert to seconds

        if (length(db_irts_for_subject) > 0) {
             subj_results_r$irt <- fit_irt_models(db_irts_for_subject)
        } else {
            log_warn("No IRTs found for subject ", subj_id, " for IRT analysis.")
            subj_results_r$irt <- NULL
        }


        # D. Rate-probability mapping
        # `calculate_rate_probability_mapping` needs `subj_trial_data` which has overall_rate
        # and `response_times_in_trial_col_r` which should be "response_times_in_trial" (list of relative secs)
        rate_prob_data_empirical <- calculate_rate_probability_mapping(
            subj_trial_data, 
            epoch_length = epoch_length_for_rate_prob,
            response_times_in_trial_col = response_times_in_trial_col_r, # from function args
            trial_start_col = trial_start_col_r, # from function args (seconds)
            trial_duration_col = "trial_duration_val" # from prepare_response_data (seconds)
        )
        
        if(!is.null(rate_prob_data_empirical) && nrow(rate_prob_data_empirical) > 0) {
            subj_results_r$rate_probability <- test_rate_probability_relation(
                rate_prob_data_empirical$mean_rate_in_bin, 
                rate_prob_data_empirical$probability,
                delta_t_epoch = epoch_length_for_rate_prob
            )
            subj_results_r$rate_probability$empirical_mapping <- rate_prob_data_empirical
        } else {
            subj_results_r$rate_probability <- NULL
        }
        results[[paste0("subject_", subj_id)]] <- subj_results_r
    } # End subject loop
    
    # Aggregate results (similar to Python version if needed, or just return per-subject)
    # For now, just returning per-subject results
    log_info("Killeen framework R analysis complete.")
    return(results)
}

# --- Main Execution Block ---
main <- function() {
    log_info("--- R Analysis Script Started ---")
    log_info("Options received: ")
    log_info("  Subject: ", ifelse(is.null(opt$subject), "ALL", opt$subject))
    log_info("  Session: ", ifelse(is.null(opt$session), "ALL (if subject specified, else ALL)", opt$session))
    log_info("  DB Path: ", opt$db_path)
    log_info("  Output JSON: ", ifelse(is.null(opt$output_json), "None (print to console)", opt$output_json))

    db_connection <- connect_db(opt$db_path)
    
    # Load data based on subject/session filters
    # If only subject is given, load all sessions for that subject
    # If only session is given, load that session (subject must be inferable or also given)
    # If neither, load all data (potentially very large!)
    
    current_subject_filter <- opt$subject
    current_session_filter <- opt$session

    if (is.null(current_subject_filter) && !is.null(current_session_filter)) {
        log_warn("Session filter provided without subject filter. This might lead to unexpected results if session IDs are not globally unique.")
        # Potentially try to infer subject from session, or just proceed.
    }
    
    # For this prototype, if --subject is not given, we analyze ALL subjects found in DB.
    # If --session is given, it further filters data for THAT subject.
    
    all_data_from_db <- load_data_from_db(db_connection, 
                                          subject_id = current_subject_filter, # Can be NULL
                                          session_id = current_session_filter  # Can be NULL
                                          )
    disconnect_db(db_connection) # Disconnect once data is loaded

    if (nrow(all_data_from_db) == 0) {
        log_warn("No data loaded from database. Exiting R analysis.")
        return(NULL)
    }
    
    # Run the main Killeen analysis function
    # It will loop through subjects found in all_data_from_db
    analysis_results <- analyze_response_strength_r(
        data_df = all_data_from_db,
        epoch_length_for_rate_prob = 1, # Default, can be parameterized
        responses_count_col_r = "responses", # This is the count column name in `all_data_from_db`
        time_to_first_response_col_r = "time_to_first_response",
        trial_duration_col_r = "trial_duration",
        trial_start_col_r = "trial_start",
        trial_end_col_r = "trial_end",
        response_times_in_trial_col_r = "response_times_in_trial"
    )
    
    if (!is.null(analysis_results)) {
        if (!is.null(opt$output_json)) {
            tryCatch({
                json_output <- jsonlite::toJSON(analysis_results, auto_unbox = TRUE, pretty = TRUE)
                write(json_output, file = opt$output_json)
                log_info("Analysis results written to JSON file: ", opt$output_json)
            }, error = function(e) {
                log_error("Failed to write results to JSON: ", e$message)
                log_info("Printing results to console instead:")
                print(analysis_results)
            })
        } else {
            log_info("--- Analysis Results (R) ---")
            # Pretty print results (can be very long)
            # print(str(analysis_results)) # For structure
            # For now, just indicate completion. Python will capture stdout.
            log_info("Results generated. If not writing to JSON, Python will capture this log.")
            # To make it easier for Python to parse, one could print a specific summary:
            if(length(analysis_results) > 0 && !is.null(analysis_results[[1]]$markov)) {
                 log_info(paste("Example Markov p(1|1) for first subject:", analysis_results[[1]]$markov$p_1_given_1))
            }
        }
    } else {
        log_warn("Analysis did not produce any results.")
    }
    
    log_info("--- R Analysis Script Finished ---")
}

# --- Execute Main ---
if (!interactive()){
    main()
}