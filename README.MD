# DTT Response Strength Analysis System

## Project Overview

This project aims to develop a standalone DTT (Discrete Trial Training) application. The application will automatically capture precise timing data from DTT sessions, store this data efficiently, and then facilitate response strength analysis using the <PERSON><PERSON> et al. (2002) framework. The system integrates Python-based data collection and GUI (using Kivy) with R-based statistical analysis, using SQLite as the intermediary database.

**Core Goals:**
1.  **Data Collection:** Implement a user-friendly DTT interface in Python (Kivy) for various tasks (e.g., clothes sorting).
2.  **Precise Timing:** Ensure accurate capture of all relevant timestamps (trial start/end, SD presentation, responses).
3.  **Data Storage:** Store collected data robustly in an SQLite database.
4.  **Response Strength Analysis:** Integrate an R-based analysis module based on the <PERSON><PERSON> et al. (2002) framework to calculate metrics like response probability, latency distributions, IRT distributions, and rate-probability mappings.
5.  **Reporting:** (Future) Generate reports summarizing the analysis results.

## Current Status (Prototype - Backend Focus)

*   **Python Project Structure:** Established as per the setup document.
*   **SQLite Database (`src/data/database.py`):**
    *   Schema implemented (subjects, sessions, trials tables).
    *   Core CRUD (Create, Read, Update, Delete) operations for subjects, sessions, and trials are functional.
    *   Basic logging and error handling are in place.
*   **R Analysis Framework (`analysis/killeen_framework.R`):**
    *   Core functions for Killeen et al. analysis (data preparation, Markov models, latency fitting, IRT fitting, rate-probability mapping) have been adapted and revised.
    *   The R script can be called from Python.
    *   It can connect to the SQLite database, load data based on subject/session filters, and perform analyses.
    *   Includes command-line argument parsing for filtering.
*   **Python-R Integration (`src/main.py`):**
    *   `main.py` can initialize the database.
    *   Includes a function to simulate a DTT session and populate the database with test data.
    *   Includes a function (`trigger_r_analysis`) to execute the R analysis script via `subprocess`, passing relevant filters.
*   **GUI and Task Stubs:** Python modules for Kivy GUI (`src/gui/`) and DTT tasks (`src/tasks/`) are placeholders and require full implementation.

## Project Structure
Use code with caution.
Markdown
dtt_response_system/
|-- dtt_env/ # Virtual environment (user-managed)
|-- src/ # Main Python source code
| |-- init.py
| |-- main.py # Application entry point, Kivy App class (eventually)
| |-- gui/ # Kivy GUI components
| | |-- init.py
| | |-- main_screen.py
| | |-- trial_screen.py
| | |-- settings_screen.py
| |-- data/ # Data handling modules
| | |-- init.py
| | |-- database.py # SQLite operations (Implemented)
| | |-- models.py # Pydantic/dataclass models for data validation (Optional)
| | |-- export.py # Data export utilities (e.g., to CSV)
| |-- tasks/ # DTT task implementations
| | |-- init.py
| | |-- base_task.py # Abstract base class for tasks
| | |-- clothes_sorting.py # Example task
| |-- utils/ # Utility functions
| | |-- init.py
| | |-- timing.py # Precise timing functions
| | |-- audio.py # Audio feedback functions
|-- assets/ # Static files
| |-- images/ # Images for tasks, icons
| |-- sounds/ # Audio files for feedback
| |-- data/ # Default location for the SQLite database
| |-- dtt_data.db # SQLite Database file
|-- analysis/ # R analysis scripts
| |-- response_strength_analysis.R # Main R script callable by Python
| |-- killeen_framework.R # Core Killeen analysis functions (Implemented)
| |-- report_generation.R # RMarkdown or other reporting scripts (Future)
|-- tests/ # Unit and integration tests
| |-- test_database.py # (Partially covered by database.py's main block)
| |-- test_tasks.py
| |-- test_timing.py
|-- docs/ # Documentation
| |-- user_manual.md
| |-- DTT_Setup_Document.pdf # (Original project plan)
|-- requirements.txt # Python dependencies
|-- build_config.py # PyInstaller specification file
|-- README.md # This file
## Next Steps & Development Phases

This project will be developed iteratively. The following outlines the key phases and tasks:

### Phase 1: Core Foundation (Partially Complete - Backend)

*   [x] Set up Python and R development environments.
*   [x] Create basic Python project structure.
*   [x] Implement SQLite database layer (`database.py`) with table creation and CRUD operations.
*   [ ] **Refine R connectivity and data loading in `killeen_framework.R`**:
    *   Thoroughly test `load_data_from_db` with various data scenarios.
    *   Ensure all necessary data transformations (e.g., JSON parsing, unit conversions for time, creating derived columns like `responses` count from `multiple_responses`) are robustly handled for the Killeen R functions.
    *   Solidify IRT extraction logic within the R script (from `all_response_times` in the database).
*   [ ] **Implement Basic Kivy GUI Framework (`src/gui/`):**
    *   Create a simple application window (`src/main.py` - `DTTApp` class).
    *   Develop `main_screen.py` with basic navigation (e.g., buttons for "New Session", "View Subjects", "Settings").
    *   Create placeholder screens for `trial_screen.py` and `settings_screen.py`.
*   [ ] **Test R Connectivity from Python GUI (Simple Trigger):**
    *   Add a button in the Kivy GUI to trigger a test R analysis (e.g., on sample data or the entire DB).

**Deliverables for Phase 1 Completion:**
*   Functional `database.py` module.
*   R script (`killeen_framework.R`) capable of connecting to the DB, loading data, and running basic Killeen analyses on simulated/test data.
*   Python script (`main.py`) that can call the R script.
*   Minimal Kivy application window with basic navigation placeholders.

### Phase 2: DTT Task Implementation (Clothes Sorting)

*   [ ] **Implement `src/tasks/clothes_sorting.py`:**
    *   Design the Kivy UI for the clothes sorting task within `trial_screen.py` (or a dedicated task screen).
    *   Handle stimulus presentation (e.g., images of clothes).
    *   Capture touch/click responses and their locations.
*   [ ] **Implement Precise Timing (`src/utils/timing.py`):**
    *   Create functions to get high-precision timestamps (e.g., using `time.perf_counter()` or platform-specific APIs if Kivy's default timing isn't sufficient).
    *   Integrate these timing functions into the task implementation for:
        *   `sd_presentation_time`
        *   `first_response_time`
        *   `all_response_times` (capture all touch events during the response window)
        *   `trial_start_time`, `trial_end_time`
*   [ ] **Implement Trial Management System:**
    *   Logic for sequencing trials (e.g., reading from a predefined list or generating randomly).
    *   Saving trial data to the SQLite database after each trial using `database.add_trial()`.
    *   Session management: Starting a session (recording `subject_id`, `session_id`, `start_time`) and ending it (updating `end_time`, `total_trials`).
*   [ ] **Implement Audio/Visual Feedback (`src/utils/audio.py` and GUI):**
    *   Functions to play `correct.wav` and `incorrect.wav`.
    *   Visual feedback on the trial screen (e.g., checkmark, X, brief highlight).

**Deliverables for Phase 2 Completion:**
*   A fully runnable "Clothes Sorting" DTT session from start to finish.
*   All timing and response data accurately captured and saved to the SQLite database.

### Phase 3: Analysis Integration & Refinement

*   [ ] **Adapt Killeen R Framework (`killeen_framework.R`):**
    *   Thoroughly test all analysis functions (`analyze_markov`, `fit_latency_models`, `fit_irt_models`, `calculate_rate_probability_mapping`, `test_rate_probability_relation`) with real data collected from the DTT app.
    *   Debug and refine data transformations within `load_data_from_db` and `prepare_response_data` in R.
    *   Implement robust error handling and logging within the R script.
*   [ ] **Create Automated Analysis Pipeline (Python -> R -> Python):**
    *   Allow users to select a subject or session from the Kivy GUI.
    *   Python triggers the R analysis script (`trigger_r_analysis`) with the selected filters.
    *   Modify the R script to output key analysis results in a structured format (e.g., JSON file or specifically formatted stdout).
    *   Python reads/parses the R output.
*   [ ] **Display Analysis Results in GUI (Basic):**
    *   Create a new Kivy screen or section in an existing screen to display summary metrics from the R analysis (e.g., Markov transition probabilities, best-fit latency model parameters).
*   [ ] **Implement Data Export (`src/data/export.py`):**
    *   Functionality to export trial data, session data, and subject data to CSV files.
    *   (Future) Export analysis results.

**Deliverables for Phase 3 Completion:**
*   Users can run a DTT session, and then trigger an R analysis on the collected data from within the Python application.
*   Key summary results from the R analysis are displayed back in the Python GUI.
*   Data export functionality.

### Phase 4: Testing, Refinement, and Packaging

*   [ ] **Comprehensive Testing:**
    *   Unit tests for `database.py`, `timing.py`, and core logic in tasks.
    *   Integration tests for the DTT session flow (Python) and the Python-R analysis pipeline.
    *   User acceptance testing (UAT) on the target hardware (Surface Book 3), focusing on:
        *   Timing accuracy.
        *   Database integrity.
        *   Touch responsiveness.
        *   Multi-session data accumulation.
        *   R analysis accuracy verification against manual checks or established results.
        *   Error handling and recovery.
*   [ ] **Performance Optimization:**
    *   Profile Python and R code if performance issues arise (e.g., long analysis times, GUI lag).
*   [ ] **User Interface Polish:**
    *   Improve the Kivy GUI for better usability and aesthetics.
*   [ ] **Documentation (`docs/user_manual.md`):**
    *   Write a user manual explaining how to use the DTT application and interpret the analysis.
*   [ ] **Build and Deployment (`build_config.py`):**
    *   Use PyInstaller (or similar) to package the Python application into a standalone executable for Windows.
    *   Ensure R and its dependencies can be correctly located or bundled if aiming for a fully portable solution (this can be complex). A more straightforward approach is to require R to be pre-installed.

**Deliverables for Phase 4 Completion:**
*   A stable, tested, and packaged DTT application.
*   User documentation.

## Key Technical Considerations & Challenges

*   **Precise Timing:** Achieving and validating millisecond-level timing accuracy in Python/Kivy, especially for response capture, is critical and can be challenging depending on the OS and hardware.
*   **R Dependency Management for Deployment:** If creating a standalone package, managing the R environment (R itself, RSQLite, and other R packages) for end-users who may not have R installed can be complex. The simplest approach is to list R and its packages as prerequisites.
*   **Data Format for IRT/Rate-Prob Mapping:**
    *   **IRTs:** The Killeen R framework needs a flat vector of *all* inter-response times for a subject/session for `fit_irt_models`. The Python data collection must record *all* response timestamps accurately. The R script's `load_data_from_db` will need to correctly parse the `all_response_times` (JSON array of absolute timestamps from the DB) and calculate session-wide IRTs.
    *   **Rate-Probability Mapping:** The `calculate_rate_probability_mapping` function in R needs trial-level data that includes `overall_rate` and a list/vector of response timestamps *within each trial* (ideally relative to trial start and in seconds) to correctly sample epochs.
*   **Kivy GUI Development:** Requires learning Kivy's event-driven model, widget system, and layout managers.
*   **Error Handling and Logging:** Robust logging in both Python and R is essential for debugging.

## Setup and Running

**1. Python Environment (as per `DTT_Setup_Document.pdf`)**
   *   Install Python (3.9+).
   *   Create a virtual environment (`python -m venv dtt_env`).
   *   Activate it (`dtt_env\Scripts\activate` on Windows).
   *   Install dependencies: `pip install -r requirements.txt` (after `requirements.txt` is populated with Kivy, pandas, numpy, etc.).
      *   Kivy specific Windows dependencies: `pip install kivy-deps.angle kivy-deps.glew kivy-deps.gstreamer`

**2. R Environment (as per `DTT_Setup_Document.pdf`)**
   *   Install R (4.0+).
   *   Open R console and run:
     ```R
     install.packages(c("RSQLite", "DBI", "tidyverse", "markovchain", "fitdistrplus", "jsonlite", "optparse"))
     ```

**3. Running the Prototype (Current Backend Focus):**
   *   To initialize the database and run the test operations in `database.py`:
     ```bash
     python src/data/database.py
     ```
   *   To run a simulated DTT session and trigger R analysis:
     ```bash
     python src/main.py
     ```
     (This will create/use `assets/data/dtt_data.db` and call `analysis/response_strength_analysis.R`)

This README should provide a clear roadmap for the project. Good luck!