# src/gui/trial_screen.py
"""
Trial screen for DTT task presentation
"""

from kivy.uix.floatlayout import FloatLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.widget import Widget
from kivy.graphics import Color, Rectangle, Ellipse
from kivy.clock import Clock
from kivy.properties import ObjectProperty, StringProperty, NumericProperty
import logging
from typing import Optional, Callable, Dict, Any

from tasks.base_task import BaseTask, TrialData, TrialResult
from tasks.clothes_sorting import ClothesSortingTask

logger = logging.getLogger(__name__)


class StimulusWidget(Widget):
    """Widget for displaying task stimuli."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.stimulus_data = None
        self.bind(pos=self.update_graphics, size=self.update_graphics)

    def set_stimulus(self, stimulus_data: Dict[str, Any]):
        """Set the stimulus data and update display."""
        self.stimulus_data = stimulus_data
        self.update_graphics()

    def update_graphics(self, *args):
        """Update the visual representation of the stimulus."""
        self.canvas.clear()

        if not self.stimulus_data:
            return

        with self.canvas:
            # Draw clothing item (simplified as colored circle)
            color_rgb = self.stimulus_data.get("color_rgb", (1, 1, 1))
            Color(*color_rgb, 1)  # RGB + alpha

            # Center the stimulus
            size = min(self.width, self.height) * 0.3
            pos_x = self.center_x - size/2
            pos_y = self.center_y - size/2

            Ellipse(pos=(pos_x, pos_y), size=(size, size))

        logger.debug(f"Updated stimulus display: {self.stimulus_data.get('color')} {self.stimulus_data.get('item')}")


class SortingBinWidget(Widget):
    """Widget representing a sorting bin."""

    bin_color = StringProperty("")

    def __init__(self, bin_color: str, **kwargs):
        super().__init__(**kwargs)
        self.bin_color = bin_color
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.update_graphics()

    def update_graphics(self, *args):
        """Update the visual representation of the bin."""
        self.canvas.clear()

        with self.canvas:
            # Draw bin outline
            Color(0.5, 0.5, 0.5, 1)  # Gray outline
            Rectangle(pos=self.pos, size=self.size)

            # Draw bin color
            if self.bin_color == "red":
                Color(1, 0, 0, 0.7)
            elif self.bin_color == "blue":
                Color(0, 0, 1, 0.7)
            elif self.bin_color == "green":
                Color(0, 1, 0, 0.7)
            elif self.bin_color == "yellow":
                Color(1, 1, 0, 0.7)
            else:
                Color(0.8, 0.8, 0.8, 0.7)  # Default gray

            # Fill bin with color
            margin = 5
            Rectangle(
                pos=(self.x + margin, self.y + margin),
                size=(self.width - 2*margin, self.height - 2*margin)
            )


class TrialScreen(FloatLayout):
    """
    Screen for conducting DTT trials.
    Handles stimulus presentation, response collection, and feedback.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Task management
        self.current_task: Optional[BaseTask] = None
        self.current_trial_data: Optional[TrialData] = None
        self.trial_in_progress = False

        # Callbacks
        self.on_trial_complete: Optional[Callable[[TrialData], None]] = None
        self.on_session_complete: Optional[Callable] = None

        # UI elements
        self.stimulus_widget = None
        self.bin_widgets = {}
        self.instruction_label = None
        self.feedback_label = None
        self.control_buttons = {}

        self.setup_ui()
        logger.info("TrialScreen initialized")

    def setup_ui(self):
        """Set up the user interface elements."""
        # Instruction label at top
        self.instruction_label = Label(
            text="Touch the correct color bin for the item",
            size_hint=(1, 0.1),
            pos_hint={'x': 0, 'top': 1},
            font_size='18sp'
        )
        self.add_widget(self.instruction_label)

        # Feedback label (initially hidden)
        self.feedback_label = Label(
            text="",
            size_hint=(0.5, 0.1),
            pos_hint={'center_x': 0.5, 'y': 0.1},
            font_size='24sp'
        )
        self.add_widget(self.feedback_label)

        # Stimulus area (center)
        self.stimulus_widget = StimulusWidget(
            size_hint=(0.6, 0.6),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        self.add_widget(self.stimulus_widget)

        # Control buttons
        self.control_buttons['start_trial'] = Button(
            text="Start Trial",
            size_hint=(0.2, 0.08),
            pos_hint={'x': 0.02, 'y': 0.02},
            on_press=self.start_trial_pressed
        )
        self.add_widget(self.control_buttons['start_trial'])

        self.control_buttons['end_session'] = Button(
            text="End Session",
            size_hint=(0.2, 0.08),
            pos_hint={'x': 0.78, 'y': 0.02},
            on_press=self.end_session_pressed
        )
        self.add_widget(self.control_buttons['end_session'])

    def set_task(self, task: BaseTask):
        """Set the current task."""
        self.current_task = task
        self.current_task.on_trial_complete = self.handle_trial_complete
        self.current_task.on_session_complete = self.handle_session_complete

        # Update instruction text
        if hasattr(task, 'get_task_instructions'):
            self.instruction_label.text = task.get_task_instructions()

        # Set up sorting bins for clothes sorting task
        if isinstance(task, ClothesSortingTask):
            self.setup_sorting_bins(task)

        logger.info(f"Set task: {task.task_name}")

    def setup_sorting_bins(self, task: ClothesSortingTask):
        """Set up sorting bins for clothes sorting task."""
        # Clear existing bins
        for bin_widget in self.bin_widgets.values():
            self.remove_widget(bin_widget)
        self.bin_widgets = {}

        # Create new bins
        for bin_color, bin_data in task.bins.items():
            bin_widget = SortingBinWidget(
                bin_color=bin_color,
                size_hint=(0.15, 0.2),
                pos_hint={
                    'x': bin_data['position'][0] - 0.075,  # Center the bin
                    'y': bin_data['position'][1] - 0.1
                }
            )
            self.bin_widgets[bin_color] = bin_widget
            self.add_widget(bin_widget)

        logger.debug(f"Set up {len(self.bin_widgets)} sorting bins")

    def start_trial_pressed(self, instance):
        """Handle start trial button press."""
        if not self.current_task:
            logger.warning("No task set")
            return

        if self.trial_in_progress:
            logger.warning("Trial already in progress")
            return

        self.start_trial()

    def start_trial(self):
        """Start a new trial."""
        if not self.current_task:
            return

        try:
            # Start trial in task
            self.current_trial_data = self.current_task.start_trial()
            self.trial_in_progress = True

            # Present stimulus
            sd_time = self.current_task.present_trial_stimulus(self.current_trial_data)

            # Update stimulus display
            self.stimulus_widget.set_stimulus(self.current_trial_data.stimulus_data)

            # Clear feedback
            self.feedback_label.text = ""

            # Disable start button
            self.control_buttons['start_trial'].disabled = True

            logger.info(f"Started trial {self.current_trial_data.trial_number}")

        except Exception as e:
            logger.error(f"Error starting trial: {e}")
            self.trial_in_progress = False

    def on_touch_down(self, touch):
        """Handle touch events for response collection."""
        # Let parent handle first
        if super().on_touch_down(touch):
            return True

        # Only handle touches during trials
        if not self.trial_in_progress or not self.current_task:
            return False

        # Convert touch position to normalized coordinates
        norm_x = touch.x / self.width
        norm_y = touch.y / self.height

        # Check if touch is on a sorting bin (for clothes sorting)
        if isinstance(self.current_task, ClothesSortingTask):
            response_data = self.current_task.handle_touch((norm_x, norm_y))

            if response_data.get('selected_bin'):
                # Record response
                result = self.current_task.record_response(response_data, self.current_trial_data)

                # Complete trial
                self.complete_trial()
                return True

        return False

    def complete_trial(self):
        """Complete the current trial."""
        if not self.trial_in_progress or not self.current_task:
            return

        try:
            # Complete trial in task
            self.current_task.complete_trial(self.current_trial_data)

            # Show feedback
            if self.current_trial_data.result == TrialResult.CORRECT:
                self.feedback_label.text = "Correct!"
                self.feedback_label.color = (0, 1, 0, 1)  # Green
            elif self.current_trial_data.result == TrialResult.INCORRECT:
                self.feedback_label.text = "Try again!"
                self.feedback_label.color = (1, 0, 0, 1)  # Red
            else:
                self.feedback_label.text = "No response"
                self.feedback_label.color = (1, 1, 0, 1)  # Yellow

            # Clear feedback after delay
            Clock.schedule_once(self.clear_feedback, 2.0)

            # Re-enable start button after delay
            Clock.schedule_once(self.enable_start_button, 2.0)

            self.trial_in_progress = False

            logger.info(f"Completed trial {self.current_trial_data.trial_number}: {self.current_trial_data.result}")

        except Exception as e:
            logger.error(f"Error completing trial: {e}")
            self.trial_in_progress = False

    def clear_feedback(self, dt):
        """Clear feedback text."""
        self.feedback_label.text = ""

    def enable_start_button(self, dt):
        """Re-enable start trial button."""
        self.control_buttons['start_trial'].disabled = False

    def handle_trial_complete(self, trial_data: TrialData):
        """Handle trial completion callback from task."""
        if self.on_trial_complete:
            self.on_trial_complete(trial_data)

    def handle_session_complete(self, trial_data_list):
        """Handle session completion callback from task."""
        if self.on_session_complete:
            self.on_session_complete()

    def end_session_pressed(self, instance):
        """Handle end session button press."""
        if self.current_task and self.current_task.session_active:
            self.current_task.end_session()
            self.feedback_label.text = "Session ended"
            self.feedback_label.color = (0, 0, 1, 1)  # Blue
            Clock.schedule_once(self.clear_feedback, 3.0)
