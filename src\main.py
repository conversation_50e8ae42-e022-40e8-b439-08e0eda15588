# src/main.py
import logging
import time
import uuid
import datetime
import subprocess # For calling R
import pandas as pd
import json # For passing complex data to R if needed

from data import database # Our database module

# --- Kivy Imports (will be used later) ---
# from kivy.app import App
# from kivy.uix.label import Label
# from gui.main_screen import MainScreen # Example

# --- Logging Setup ---
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.DEBUG, format=LOG_FORMAT)
logger = logging.getLogger(__name__)


R_SCRIPT_PATH = "analysis/response_strength_analysis.R" # Relative to project root

def initialize_system():
    """Initializes the system, e.g., creates database tables."""
    logger.info("DTT Response Strength Analysis System - Initializing...")
    database.create_tables()
    logger.info("System Initialized.")

def run_dtt_session(subject_id, session_id_prefix="SESS"):
    """Simulates a DTT session and records data."""
    logger.info(f"--- Starting DTT Session for Subject: {subject_id} ---")
    session_id = f"{session_id_prefix}{uuid.uuid4().hex[:8].upper()}"
    session_date = datetime.date.today().isoformat()
    start_time_dt = datetime.datetime.now()
    start_time_iso = start_time_dt.isoformat()

    database.start_session(session_id, subject_id, session_date, start_time_iso, ["simulated_task"])

    num_trials = 10 # Simulate 10 trials
    trial_data_for_r = [] # To collect data for R analysis

    for i in range(1, num_trials + 1):
        logger.debug(f"Running Trial {i} for session {session_id}")
        trial_start_ts = time.time() * 1000
        time.sleep(0.5) # Simulate SD presentation delay
        sd_presentation_ts = time.time() * 1000

        # Simulate response
        response_occurred = 1 if__import_system_random.random() > 0.2 else 0 # 80% chance of response
        first_response_ts = None
        all_responses_ts_list = []
        latency_calc = None

        if response_occurred:
            response_delay = _import_system_random.uniform(0.5, 3.0) # seconds
            time.sleep(response_delay)
            first_response_ts = time.time() * 1000
            all_responses_ts_list.append(first_response_ts)
            latency_calc = first_response_ts - sd_presentation_ts
            
            # Simulate potential multiple responses
            if _import_system_random.random() > 0.7:
                time.sleep(_import_system_random.uniform(0.2, 1.0))
                all_responses_ts_list.append(time.time() * 1000)


        time.sleep(_import_system_random.uniform(1.0, 2.0)) # Simulate rest of trial
        trial_end_ts = time.time() * 1000
        trial_duration_calc = trial_end_ts - trial_start_ts

        # Store in DB
        trial_id = database.add_trial(
            subject_id=subject_id,
            session_id=session_id,
            trial_number=i,
            task_type="simulated_task",
            target_skill="simulation",
            trial_start_time=trial_start_ts,
            sd_presentation_time=sd_presentation_ts,
            first_response_time=first_response_ts,
            all_response_times=all_responses_ts_list, # Pass the list
            trial_end_time=trial_end_ts,
            response_occurred=response_occurred,
            response_correct= 1 if response_occurred and _import_system_random.random() > 0.3 else 0,
            multiple_responses=len(all_responses_ts_list)
        )
        logger.debug(f"Trial {i} (ID: {trial_id}) data saved.")
        
        # Prepare data for R (matching Killeen_framework.R expected input)
        # Note: Killeen framework's prepare_response_data expects certain columns
        trial_dict_for_r = {
            "subject": subject_id,
            "session": session_id,
            "trial": i,
            "responses": len(all_responses_ts_list), # Count of responses in this trial
            "time_to_first_response": latency_calc / 1000 if latency_calc else None, # in seconds
            "trial_duration": trial_duration_calc / 1000 if trial_duration_calc else None, # in seconds
            "trial_start": trial_start_ts / 1000, # in seconds
            "trial_end": trial_end_ts / 1000, # in seconds
            # This is the tricky one: list of response timestamps *within this trial*, relative to trial start
            "response_times_in_trial": [(rt - trial_start_ts)/1000 for rt in all_responses_ts_list] if first_response_ts else [],
            # Add any other columns your R script's prepare_response_data might need
            # e.g. sd_presentation_time relative to trial_start
            "sd_presentation_time_relative": (sd_presentation_ts - trial_start_ts) / 1000 if sd_presentation_ts else None,
            "response_occurred_binary": response_occurred # Killeen script uses 'response'
        }
        trial_data_for_r.append(trial_dict_for_r)

    end_time_dt = datetime.datetime.now()
    end_time_iso = end_time_dt.isoformat()
    database.end_session(session_id, end_time_iso, num_trials)
    logger.info(f"--- DTT Session {session_id} Ended ---")
    return session_id, pd.DataFrame(trial_data_for_r)


def trigger_r_analysis(subject_id=None, session_id=None, db_path=None):
    """
    Triggers the R script for analysis.
    Passes subject_id and session_id as arguments to R.
    """
    if db_path is None:
        db_path = database.DATABASE_PATH
        
    logger.info(f"Triggering R analysis for Subject: {subject_id}, Session: {session_id} using DB: {db_path}")
    
    cmd = ["Rscript", R_SCRIPT_PATH]
    if subject_id:
        cmd.extend(["--subject", str(subject_id)])
    if session_id:
        cmd.extend(["--session", str(session_id)])
    cmd.extend(["--db_path", db_path])

    try:
        logger.debug(f"Running R command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8')
        logger.info("R script executed successfully.")
        logger.info("R script stdout:")
        for line in result.stdout.splitlines():
            logger.info(line)
        if result.stderr:
            logger.warning("R script stderr:")
            for line in result.stderr.splitlines():
                logger.warning(line)
        
        # Potentially parse JSON output from R if R script prints results as JSON
        # For now, just log stdout
        
    except subprocess.CalledProcessError as e:
        logger.error(f"R script execution failed with exit code {e.returncode}.")
        logger.error(f"R script stdout: {e.stdout}")
        logger.error(f"R script stderr: {e.stderr}")
    except FileNotFoundError:
        logger.error(f"Rscript command not found. Is R installed and in PATH? R_SCRIPT_PATH: {R_SCRIPT_PATH}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while running R script: {e}")

# --- Kivy App Class (Placeholder) ---
# class DTTApp(App):
#     def build(self):
#         # return Label(text='DTT Response Strength System - GUI Placeholder')
#         return MainScreen() # Will be implemented later


if __name__ == "__main__":
    initialize_system()
    
    _import_system_random = __import__("random") # For simulation

    # Example: Add a subject if not exists
    test_subject_id = "SIM_SUB001"
    if not database.get_subject(test_subject_id):
        database.add_subject(test_subject_id, "Simulated Subject")

    # Run a simulated session
    sim_session_id, df_for_r = run_dtt_session(test_subject_id)
    
    # Example of how to pass DataFrame to R (if not reading directly from DB in R)
    # temp_csv_path = "temp_r_input_data.csv"
    # df_for_r.to_csv(temp_csv_path, index=False)
    # logger.info(f"Temporary data for R saved to {temp_csv_path}")

    # Trigger R analysis for the simulated session
    trigger_r_analysis(subject_id=test_subject_id, session_id=sim_session_id)
    
    # Trigger R analysis for the whole subject
    # trigger_r_analysis(subject_id=test_subject_id)


    # --- Start Kivy App (when GUI is ready) ---
    # logger.info("Starting Kivy Application...")
    # DTTApp().run()