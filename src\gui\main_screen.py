# src/gui/main_screen.py
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
import logging
logger = logging.getLogger(__name__)

class MainScreen(BoxLayout):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.add_widget(Label(text="DTT System - Main Screen"))
        self.add_widget(Button(text="Start Session (Not Implemented)", on_press=self.start_session_pressed))
        logger.info("MainScreen initialized.")

    def start_session_pressed(self, instance):
        logger.info("Start Session button pressed - functionality to be added.")
        # Here you would switch to a subject selection or trial screen