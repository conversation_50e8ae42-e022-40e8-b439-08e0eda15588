# src/tasks/clothes_sorting.py
"""
Clothes sorting DTT task implementation
"""

import random
from typing import Dict, Any, List, Tuple
import logging
from pathlib import Path

from .base_task import BaseTask, TrialResult

logger = logging.getLogger(__name__)


class ClothesSortingTask(BaseTask):
    """
    DTT task for clothes sorting by color.

    Presents images of clothing items and requires sorting by color
    into appropriate bins/containers.
    """

    def __init__(self):
        super().__init__("clothes_sorting", "color_discrimination")

        # Define clothing items and colors
        self.clothing_items = [
            "shirt", "pants", "dress", "sock", "hat", "jacket"
        ]

        self.colors = [
            {"name": "red", "rgb": (255, 0, 0)},
            {"name": "blue", "rgb": (0, 0, 255)},
            {"name": "green", "rgb": (0, 255, 0)},
            {"name": "yellow", "rgb": (255, 255, 0)}
        ]

        # Define sorting bins (screen positions)
        self.bins = {
            "red": {"position": (0.2, 0.3), "size": (0.15, 0.2)},
            "blue": {"position": (0.8, 0.3), "size": (0.15, 0.2)},
            "green": {"position": (0.2, 0.7), "size": (0.15, 0.2)},
            "yellow": {"position": (0.8, 0.7), "size": (0.15, 0.2)}
        }

        # Current trial state
        self.current_stimulus_widget = None
        self.current_bin_widgets = {}
        self.stimulus_position = (0.5, 0.5)  # Center of screen

        logger.info("Initialized clothes sorting task")

    def generate_stimulus(self, trial_number: int) -> Dict[str, Any]:
        """
        Generate a clothing item with random color for sorting.

        Args:
            trial_number: Current trial number

        Returns:
            dict: Stimulus data including item, color, and correct bin
        """
        # Select random clothing item and color
        item = random.choice(self.clothing_items)
        color = random.choice(self.colors)

        stimulus_data = {
            "item": item,
            "color": color["name"],
            "color_rgb": color["rgb"],
            "correct_bin": color["name"],
            "stimulus_position": self.stimulus_position,
            "bins": self.bins.copy(),
            "trial_number": trial_number
        }

        logger.debug(f"Generated stimulus: {color['name']} {item}")
        return stimulus_data

    def present_stimulus(self, stimulus_data: Dict[str, Any]) -> float:
        """
        Present the clothing item stimulus on screen.

        Args:
            stimulus_data: Data describing the stimulus

        Returns:
            float: Timestamp when stimulus was presented
        """
        # In a real implementation, this would create Kivy widgets
        # For now, we'll simulate the presentation

        item = stimulus_data["item"]
        color = stimulus_data["color"]

        logger.info(f"Presenting stimulus: {color} {item}")

        # Simulate creating visual stimulus
        self.current_stimulus_widget = {
            "type": "clothing_item",
            "item": item,
            "color": color,
            "position": stimulus_data["stimulus_position"],
            "visible": True
        }

        # Simulate creating bin widgets
        for bin_color, bin_data in stimulus_data["bins"].items():
            self.current_bin_widgets[bin_color] = {
                "type": "sorting_bin",
                "color": bin_color,
                "position": bin_data["position"],
                "size": bin_data["size"],
                "active": True
            }

        # Return current timestamp
        from utils.timing import get_precise_timestamp
        return get_precise_timestamp()

    def evaluate_response(self, response_data: Dict[str, Any],
                         stimulus_data: Dict[str, Any]) -> TrialResult:
        """
        Evaluate if the participant sorted the item correctly.

        Args:
            response_data: Data about where the participant touched/clicked
            stimulus_data: Data about the presented stimulus

        Returns:
            TrialResult: CORRECT if sorted to right bin, INCORRECT otherwise
        """
        if "selected_bin" not in response_data:
            return TrialResult.NO_RESPONSE

        selected_bin = response_data["selected_bin"]
        correct_bin = stimulus_data["correct_bin"]

        if selected_bin == correct_bin:
            logger.debug(f"Correct response: {selected_bin}")
            return TrialResult.CORRECT
        else:
            logger.debug(f"Incorrect response: {selected_bin} (correct: {correct_bin})")
            return TrialResult.INCORRECT

    def cleanup_trial(self):
        """Clean up after trial completion."""
        # Remove stimulus and bin widgets
        self.current_stimulus_widget = None
        self.current_bin_widgets = {}
        logger.debug("Cleaned up trial widgets")

    def check_touch_in_bin(self, touch_pos: Tuple[float, float]) -> str:
        """
        Check which bin (if any) was touched.

        Args:
            touch_pos: Touch position as (x, y) normalized coordinates (0-1)

        Returns:
            str: Name of touched bin, or empty string if no bin touched
        """
        x, y = touch_pos

        for bin_color, bin_data in self.bins.items():
            bin_x, bin_y = bin_data["position"]
            bin_w, bin_h = bin_data["size"]

            # Check if touch is within bin bounds
            if (bin_x - bin_w/2 <= x <= bin_x + bin_w/2 and
                bin_y - bin_h/2 <= y <= bin_y + bin_h/2):
                return bin_color

        return ""

    def handle_touch(self, touch_pos: Tuple[float, float]) -> Dict[str, Any]:
        """
        Handle a touch/click event during the trial.

        Args:
            touch_pos: Touch position as (x, y) normalized coordinates

        Returns:
            dict: Response data including selected bin
        """
        selected_bin = self.check_touch_in_bin(touch_pos)

        response_data = {
            "touch_position": touch_pos,
            "selected_bin": selected_bin,
            "response_type": "touch"
        }

        logger.debug(f"Touch at {touch_pos}, selected bin: {selected_bin}")
        return response_data

    def get_task_instructions(self) -> str:
        """Get instructions for this task."""
        return ("Sort the clothing items by color. "
                "Touch the clothing item and drag it to the matching color bin, "
                "or simply touch the correct color bin.")

    def get_available_colors(self) -> List[str]:
        """Get list of available colors for this task."""
        return [color["name"] for color in self.colors]

    def get_available_items(self) -> List[str]:
        """Get list of available clothing items."""
        return self.clothing_items.copy()

    def set_difficulty_level(self, level: int):
        """
        Set task difficulty level.

        Args:
            level: Difficulty level (1-3)
                  1: 2 colors, 2 items
                  2: 3 colors, 4 items
                  3: 4 colors, 6 items
        """
        if level == 1:
            self.colors = self.colors[:2]
            self.clothing_items = self.clothing_items[:2]
        elif level == 2:
            self.colors = self.colors[:3]
            self.clothing_items = self.clothing_items[:4]
        else:  # level 3 or higher
            # Use all colors and items
            pass

        logger.info(f"Set difficulty level {level}: {len(self.colors)} colors, {len(self.clothing_items)} items")
