# src/data/database.py
import sqlite3
import json
import datetime
import logging
import os

# --- Logging Setup ---
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.DEBUG, format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# --- Database Configuration ---
DATABASE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'assets', 'data')
DATABASE_NAME = 'dtt_data.db'
DATABASE_PATH = os.path.join(DATABASE_DIR, DATABASE_NAME)


def get_db_connection():
    """Establishes a connection to the SQLite database."""
    os.makedirs(DATABASE_DIR, exist_ok=True) # Ensure directory exists
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row # Access columns by name
    logger.debug(f"Database connection established to {DATABASE_PATH}")
    return conn

def close_db_connection(conn):
    """Closes the database connection."""
    if conn:
        conn.close()
        logger.debug("Database connection closed.")

def create_tables():
    """Creates the necessary tables in the database if they don't exist."""
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Trials Table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS trials (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subject_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            trial_number INTEGER NOT NULL,
            task_type TEXT NOT NULL,
            target_skill TEXT,
            trial_start_time REAL NOT NULL,       -- Milliseconds since epoch
            sd_presentation_time REAL NOT NULL, -- Milliseconds since epoch
            first_response_time REAL,           -- Milliseconds since epoch
            all_response_times TEXT,            -- JSON array of timestamps (milliseconds since epoch)
            trial_end_time REAL NOT NULL,         -- Milliseconds since epoch
            response_occurred INTEGER NOT NULL,   -- 0 or 1
            response_correct INTEGER,             -- 0, 1, or NULL
            response_location TEXT,               -- Where they touched/clicked
            multiple_responses INTEGER DEFAULT 0,
            prompt_level INTEGER DEFAULT 0,       -- 0=independent, 1=gestural, etc.
            reinforcer_delivered INTEGER DEFAULT 0,
            latency_ms REAL,                      -- first_response_time - sd_presentation_time
            trial_duration_ms REAL,               -- trial_end_time - trial_start_time
            created_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (session_id) REFERENCES sessions (session_id),
            FOREIGN KEY (subject_id) REFERENCES subjects (subject_id)
        );
        """)
        logger.info("Trials table checked/created.")

        # Sessions Table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS sessions (
            session_id TEXT PRIMARY KEY,
            subject_id TEXT NOT NULL,
            session_date DATE NOT NULL,
            start_time DATETIME NOT NULL,
            end_time DATETIME,
            task_types TEXT,                      -- JSON array of tasks in session
            total_trials INTEGER DEFAULT 0,
            notes TEXT,
            created_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (subject_id) REFERENCES subjects (subject_id)
        );
        """)
        logger.info("Sessions table checked/created.")

        # Subjects Table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS subjects (
            subject_id TEXT PRIMARY KEY,
            name TEXT,
            date_of_birth DATE,
            created_date DATE DEFAULT CURRENT_DATE,
            active INTEGER DEFAULT 1,
            notes TEXT
        );
        """)
        logger.info("Subjects table checked/created.")

        # Indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trials_subject_session ON trials(subject_id, session_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trials_sd_timestamp ON trials(sd_presentation_time);") # Renamed from doc
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_date ON sessions(session_date);")
        logger.info("Indexes checked/created.")

        conn.commit()
    except sqlite3.Error as e:
        logger.error(f"Database table creation error: {e}")
        conn.rollback() # Rollback changes if error occurs
    finally:
        close_db_connection(conn)

# --- CRUD Operations ---

# Subjects
def add_subject(subject_id, name=None, date_of_birth=None, notes=None):
    conn = get_db_connection()
    try:
        conn.execute("""
            INSERT INTO subjects (subject_id, name, date_of_birth, notes)
            VALUES (?, ?, ?, ?)
        """, (subject_id, name, date_of_birth, notes))
        conn.commit()
        logger.info(f"Subject '{subject_id}' added.")
        return True
    except sqlite3.IntegrityError:
        logger.warning(f"Subject '{subject_id}' already exists.")
        return False
    except sqlite3.Error as e:
        logger.error(f"Error adding subject '{subject_id}': {e}")
        conn.rollback()
        return False
    finally:
        close_db_connection(conn)

def get_subject(subject_id):
    conn = get_db_connection()
    try:
        cursor = conn.execute("SELECT * FROM subjects WHERE subject_id = ?", (subject_id,))
        subject = cursor.fetchone()
        return dict(subject) if subject else None
    except sqlite3.Error as e:
        logger.error(f"Error getting subject '{subject_id}': {e}")
        return None
    finally:
        close_db_connection(conn)

# Sessions
def start_session(session_id, subject_id, session_date, start_time, task_types=None, notes=None):
    conn = get_db_connection()
    try:
        task_types_json = json.dumps(task_types) if task_types else None
        conn.execute("""
            INSERT INTO sessions (session_id, subject_id, session_date, start_time, task_types, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (session_id, subject_id, session_date, start_time, task_types_json, notes))
        conn.commit()
        logger.info(f"Session '{session_id}' started for subject '{subject_id}'.")
        return True
    except sqlite3.IntegrityError:
        logger.warning(f"Session '{session_id}' already exists.")
        return False
    except sqlite3.Error as e:
        logger.error(f"Error starting session '{session_id}': {e}")
        conn.rollback()
        return False
    finally:
        close_db_connection(conn)

def end_session(session_id, end_time, total_trials):
    conn = get_db_connection()
    try:
        conn.execute("""
            UPDATE sessions
            SET end_time = ?, total_trials = ?
            WHERE session_id = ?
        """, (end_time, total_trials, session_id))
        conn.commit()
        logger.info(f"Session '{session_id}' ended. Total trials: {total_trials}.")
        return True
    except sqlite3.Error as e:
        logger.error(f"Error ending session '{session_id}': {e}")
        conn.rollback()
        return False
    finally:
        close_db_connection(conn)

# Trials
def add_trial(
    subject_id, session_id, trial_number, task_type, target_skill,
    trial_start_time, sd_presentation_time, trial_end_time,
    response_occurred,
    first_response_time=None, all_response_times=None, # all_response_times is a list of python timestamps
    response_correct=None, response_location=None, multiple_responses=0,
    prompt_level=0, reinforcer_delivered=0,
    notes=None
):
    conn = get_db_connection()
    try:
        # Calculate metrics
        latency_ms = None
        if first_response_time and sd_presentation_time:
            latency_ms = first_response_time - sd_presentation_time
            if latency_ms < 0: # Should not happen with correct timing
                logger.warning(f"Negative latency calculated for S:{subject_id}, Sess:{session_id}, T:{trial_number}. Latency: {latency_ms}ms. FRT: {first_response_time}, SDT: {sd_presentation_time}")
                latency_ms = 0 # Or handle as error

        trial_duration_ms = None
        if trial_end_time and trial_start_time:
            trial_duration_ms = trial_end_time - trial_start_time

        # Convert all_response_times list to JSON string
        all_response_times_json = json.dumps(all_response_times) if all_response_times else None

        conn.execute("""
            INSERT INTO trials (
                subject_id, session_id, trial_number, task_type, target_skill,
                trial_start_time, sd_presentation_time, first_response_time,
                all_response_times, trial_end_time, response_occurred,
                response_correct, response_location, multiple_responses,
                prompt_level, reinforcer_delivered, latency_ms, trial_duration_ms, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            subject_id, session_id, trial_number, task_type, target_skill,
            trial_start_time, sd_presentation_time, first_response_time,
            all_response_times_json, trial_end_time, response_occurred,
            response_correct, response_location, multiple_responses,
            prompt_level, reinforcer_delivered, latency_ms, trial_duration_ms, notes
        ))
        conn.commit()
        logger.debug(f"Trial {trial_number} added for session '{session_id}'.")
        return conn.execute("SELECT last_insert_rowid()").fetchone()[0] # Return trial_id
    except sqlite3.Error as e:
        logger.error(f"Error adding trial {trial_number} for session '{session_id}': {e}")
        conn.rollback()
        return None
    finally:
        close_db_connection(conn)

def get_trials_for_session(session_id):
    """Fetches all trials for a given session_id."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("SELECT * FROM trials WHERE session_id = ? ORDER BY trial_number", (session_id,))
        trials = [dict(row) for row in cursor.fetchall()]
        # Convert JSON strings back to lists for all_response_times
        for trial in trials:
            if trial.get('all_response_times'):
                try:
                    trial['all_response_times'] = json.loads(trial['all_response_times'])
                except json.JSONDecodeError:
                    logger.warning(f"Could not decode all_response_times for trial {trial['id']}: {trial['all_response_times']}")
                    trial['all_response_times'] = None # Or an empty list
        return trials
    except sqlite3.Error as e:
        logger.error(f"Error getting trials for session '{session_id}': {e}")
        return []
    finally:
        close_db_connection(conn)

def get_trials_for_subject(subject_id):
    """Fetches all trials for a given subject_id across all sessions."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("SELECT * FROM trials WHERE subject_id = ? ORDER BY session_id, trial_number", (subject_id,))
        trials = [dict(row) for row in cursor.fetchall()]
        for trial in trials:
            if trial.get('all_response_times'):
                try:
                    trial['all_response_times'] = json.loads(trial['all_response_times'])
                except json.JSONDecodeError:
                     logger.warning(f"Could not decode all_response_times for trial {trial['id']}: {trial['all_response_times']}")
                     trial['all_response_times'] = None
        return trials
    except sqlite3.Error as e:
        logger.error(f"Error getting trials for subject '{subject_id}': {e}")
        return []
    finally:
        close_db_connection(conn)

if __name__ == '__main__':
    # Ensure the logger for this module is also configured if run directly
    # (it should be by the global basicConfig, but being explicit doesn't hurt)
    if not logging.getLogger().hasHandlers(): # Check if root logger has handlers
        logging.basicConfig(level=logging.DEBUG, format=LOG_FORMAT)
    
    logger.info("--- database.py executed directly ---")
    logger.info("Initializing database and creating tables...")
    create_tables()
    logger.info("Database setup complete (tables checked/created).")

    # --- Example Usage (for testing database.py directly) ---
    logger.info("--- Testing Database CRUD Operations ---")

    # Add a subject
    logger.info("Attempting to add subject SUB001...")
    add_subject_success = add_subject("SUB001", "Alice Test", "2018-05-15", "Initial test subject")
    if add_subject_success:
        logger.info("Subject SUB001 added successfully or already existed.")
    else:
        logger.error("Failed to add subject SUB001.")

    retrieved_subject = get_subject("SUB001")
    if retrieved_subject:
        logger.info(f"Retrieved Subject SUB001: {retrieved_subject}")
    else:
        logger.warning("Could not retrieve subject SUB001 after attempting to add.")

    # Start a session
    session_time_start_dt = datetime.datetime.now()
    session_time_start_iso = session_time_start_dt.isoformat()
    session_date_iso = session_time_start_dt.date().isoformat()
    
    logger.info("Attempting to start session SESS001...")
    start_session_success = start_session("SESS001", "SUB001", session_date_iso, session_time_start_iso, ["clothes_sorting"], "Test session")
    if start_session_success:
        logger.info("Session SESS001 started successfully or already existed.")
    else:
        logger.error("Failed to start session SESS001.")


    # Add some trials (timestamps would typically be from time.time() * 1000)
    # Timestamps are in milliseconds since epoch
    # Using a more controlled way to get timestamps for testing
    base_time = datetime.datetime.now()
    def get_test_ts(offset_seconds):
        return (base_time + datetime.timedelta(seconds=offset_seconds)).timestamp() * 1000

    logger.info("Adding trial 1...")
    trial1_id = add_trial("SUB001", "SESS001", 1, "clothes_sorting", "shirt_color",
                          get_test_ts(0), get_test_ts(1), get_test_ts(10), response_occurred=1, first_response_time=get_test_ts(3.5),
                          all_response_times=[get_test_ts(3.5), get_test_ts(4.1)], response_correct=1, response_location="blue_bin")
    logger.info(f"Trial 1 added with ID: {trial1_id}")

    logger.info("Adding trial 2...")
    trial2_id = add_trial("SUB001", "SESS001", 2, "clothes_sorting", "shirt_color",
                          get_test_ts(11), get_test_ts(12), get_test_ts(21), response_occurred=1, first_response_time=get_test_ts(15),
                          all_response_times=[get_test_ts(15)], response_correct=0, response_location="red_bin")
    logger.info(f"Trial 2 added with ID: {trial2_id}")

    logger.info("Adding trial 3...")
    trial3_id = add_trial("SUB001", "SESS001", 3, "clothes_sorting", "shirt_color",
                          get_test_ts(22), get_test_ts(23), get_test_ts(32), response_occurred=0) # No response
    logger.info(f"Trial 3 added with ID: {trial3_id}")


    session_trials = get_trials_for_session("SESS001")
    if session_trials:
        logger.info(f"Trials for SESS001 (count: {len(session_trials)}):")
        for t in session_trials:
            # logger.info(f"  Trial {t['trial_number']}: Response Occurred={t['response_occurred']}, Latency={t['latency_ms']}ms, all_response_times={t['all_response_times']}")
            # More concise logging for direct run
            logger.info(f"  T#{t['trial_number']} RO={t['response_occurred']} Lat={t['latency_ms']}ms ARTs={t.get('all_response_times', 'None')}")

    else:
        logger.warning("No trials retrieved for SESS001.")

    # End the session
    session_time_end_dt = datetime.datetime.now()
    session_time_end_iso = session_time_end_dt.isoformat()
    logger.info("Attempting to end session SESS001...")
    end_session_success = end_session("SESS001", session_time_end_iso, len(session_trials) if session_trials else 0)
    if end_session_success:
        logger.info("Session SESS001 ended successfully.")
    else:
        logger.error("Failed to end session SESS001.")


    subject_trials_all = get_trials_for_subject("SUB001")
    if subject_trials_all:
        logger.info(f"All trials for SUB001 (count: {len(subject_trials_all)}). First trial details: {dict(subject_trials_all[0]) if len(subject_trials_all) > 0 else 'N/A'}")
    else:
        logger.warning("No trials retrieved for SUB001 overall.")
    
    logger.info("--- Finished testing Database CRUD Operations ---")
    logger.info(f"Database file should be at: {DATABASE_PATH}")